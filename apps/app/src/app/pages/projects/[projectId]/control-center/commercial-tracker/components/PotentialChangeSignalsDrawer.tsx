import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema, PotentialChangeSchema } from '@shape-construction/api/src';
import { getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryKey } from '@shape-construction/api/src/hooks';
import { But<PERSON>, Drawer, IconButton } from '@shape-construction/arch-ui';
import {
  ArrowLeftIcon,
  ArrowsMaximizeIcon,
  ArrowsMinimizeIcon,
  DocumentArrowDownIcon,
  InboxArrowDownIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useModal } from '@shape-construction/hooks';
import { useQueryClient, useSuspenseInfiniteQuery } from '@tanstack/react-query';
import { useCurrentProject } from 'app/contexts/currentProject';
import {
  getChangeSignalsDowntimeInfiniteQueryOptions,
  getChangeSignalsInfiniteQueryOptions,
  useLinkChangeSignals,
  useUnlinkChangeSignals,
} from 'app/queries/control-center/change-signals';
import {
  useArchivePotentialChange,
  useGetPotentialChangeDetails,
} from 'app/queries/control-center/commercial-tracker';
import { useCallback, useState } from 'react';
import type { PotentialChangeSignalsStep } from '../types';
import { canArchivePotentialChange } from '../utils/potentialChangeActions';
import { ArchiveChangeConfirmationModal } from './ArchiveChangeConfirmationModal';
import { ExportChangeConfirmationModal } from './ExportChangeConfirmationModal';
import { PotentialChangeSignalsLinking } from './PotentialChangeSignalsLinking';
import { PotentialChangeSignalsList } from './PotentialChangeSignalsList';
import { UnlinkConfirmationDialog } from './UnlinkConfirmationDialog';

type PotentialChangeSignalsDrawerProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedPotentialChange: PotentialChangeSchema;
  setSelectedPotentialChange: (potentialChange: PotentialChangeSchema) => void;
};

export const PotentialChangeSignalsDrawer: React.FC<PotentialChangeSignalsDrawerProps> = ({
  isOpen,
  onClose,
  selectedPotentialChange,
  setSelectedPotentialChange,
}) => {
  const queryClient = useQueryClient();
  const [currentStep, setCurrentStep] = useState<PotentialChangeSignalsStep>('VIEW');
  const [selectedChangeSignals, setSelectedChangeSignals] = useState<ChangeSignalSchema[]>([]);
  const [fullsceen, setFullscreen] = useState(false);

  const {
    open: isUnlinkConfirmModalOpen,
    openModal: openUnlinkConfirmModal,
    closeModal: closeUnlinkConfirmModal,
  } = useModal(false);
  const {
    open: isArchiveChangeConfirmModalOpen,
    openModal: openArchiveChangeConfirmModal,
    closeModal: closeArchiveChangeConfirmModal,
  } = useModal(false);
  const {
    open: isExportChangeConfirmModalOpen,
    openModal: openExportChangeConfirmModal,
    closeModal: closeExportChangeConfirmModal,
  } = useModal(false);

  const [signalToUnlink, setSignalToUnlink] = useState<{
    signalId: ChangeSignalSchema['signalId'];
    signalType: ChangeSignalSchema['signalType'];
  } | null>(null);

  const project = useCurrentProject();
  const messages = useMessageGetter('controlCenter.commercialTracker.modals');
  const linkChangeSignalsMessages = useMessageGetter('controlCenter.changeSignals.linkStatus');

  const [selectedSignalType, setSelectedSignalType] = useState<ChangeSignalSchema['signalType']>('issue');

  const { data: potentialChange } = useGetPotentialChangeDetails(project.id, selectedPotentialChange.id);
  const issuesQuery = useSuspenseInfiniteQuery(getChangeSignalsInfiniteQueryOptions(project.id));
  const downtimeQuery = useSuspenseInfiniteQuery(getChangeSignalsDowntimeInfiniteQueryOptions(project.id));
  const currentQuery = selectedSignalType === 'issue' ? issuesQuery : downtimeQuery;
  const currentChangeSignals = currentQuery.data.signals ?? [];

  const issuesCount = issuesQuery.data.total;
  const downtimeCount = downtimeQuery.data.total;

  const { mutateAsync: linkChangeSignals, isPending: isLinkingChangeSignals } = useLinkChangeSignals();
  const { mutateAsync: unlinkChangeSignals, isPending: isUnlinkingChangeSignals } = useUnlinkChangeSignals();
  const { mutateAsync: archivePotentialChange, isPending: isArchivePending } = useArchivePotentialChange();

  const resetState = () => {
    setCurrentStep('VIEW');
    setSelectedChangeSignals([]);
    setSelectedSignalType('issue');
    setFullscreen(false);
  };

  const onDrawerClose = () => {
    resetState();
    onClose();
  };

  const onLinkChangeSignals = async () => {
    try {
      const potentialChangeResponse = await linkChangeSignals({
        projectId: project.id,
        potentialChangeId: selectedPotentialChange.id,
        data: {
          change_signals: selectedChangeSignals.map((signal) => ({
            change_signal_type: signal.signalType,
            change_signal_id: signal.signalId,
          })),
        },
      });

      await queryClient.invalidateQueries({
        queryKey: getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryKey(
          project.id,
          selectedPotentialChange.id
        ),
      });

      showSuccessToast({
        message: linkChangeSignalsMessages('linking.success'),
      });
      setSelectedPotentialChange(potentialChangeResponse);
      resetState();
    } catch (error) {
      showErrorToast({
        message: linkChangeSignalsMessages('linking.failed'),
      });
    }
  };

  const onUnlinkChangeSignalClick = async (
    signalId: ChangeSignalSchema['signalId'],
    signalType: ChangeSignalSchema['signalType']
  ) => {
    setSignalToUnlink({ signalId, signalType });
    openUnlinkConfirmModal();
  };

  const handleUnlinkConfirm = async () => {
    if (!signalToUnlink) return;
    try {
      await unlinkChangeSignals({
        projectId: project.id,
        potentialChangeId: selectedPotentialChange.id,
        data: {
          change_signals: [
            {
              change_signal_id: signalToUnlink.signalId,
              change_signal_type: signalToUnlink.signalType,
            },
          ],
        },
      });
      await queryClient.invalidateQueries({
        queryKey: getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryKey(
          project.id,
          selectedPotentialChange.id
        ),
      });
      showSuccessToast({
        message: linkChangeSignalsMessages('unlinking.success'),
      });
    } catch {
      showErrorToast({
        message: linkChangeSignalsMessages('unlinking.failed'),
      });
    } finally {
      closeUnlinkConfirmModal();
      setSignalToUnlink(null);
    }
  };

  const onArchive = () => {
    closeArchiveChangeConfirmModal();
    archivePotentialChange({
      projectId: project.id,
      potentialChangeId: selectedPotentialChange.id,
    });
    onDrawerClose();
  };

  const canArchive = canArchivePotentialChange(selectedPotentialChange);

  const handleLoadMore = useCallback(() => {
    if (currentQuery.hasNextPage && !currentQuery.isFetchingNextPage) {
      currentQuery.fetchNextPage();
    }
  }, [currentQuery]);

  const toggleFullscreen = () => {
    setFullscreen(!fullsceen);
  };

  const drawerRightContent = (
    <div className="flex items-center gap-2">
      {canArchive && (
        <Button
          onClick={openArchiveChangeConfirmModal}
          color="secondary"
          size="xs"
          variant="outlined"
          leadingIcon={InboxArrowDownIcon}
          tabIndex={-1}
          disabled={isArchivePending}
          aria-label={messages('potentialChangeLinkSignalsDrawer.archiveCTA')}
        >
          {messages('potentialChangeLinkSignalsDrawer.archiveCTA')}
        </Button>
      )}
      <Button
        onClick={openExportChangeConfirmModal}
        color="primary"
        size="xs"
        variant="contained"
        leadingIcon={DocumentArrowDownIcon}
        tabIndex={-1}
        disabled={isArchivePending}
        aria-label={messages('exportChange.exportCTA')}
      >
        {messages('exportChange.exportCTA')}
      </Button>
      <IconButton
        shape="square"
        color="secondary"
        size="xs"
        variant="outlined"
        icon={fullsceen ? ArrowsMinimizeIcon : ArrowsMaximizeIcon}
        onClick={toggleFullscreen}
        tabIndex={-1}
      />
    </div>
  );

  const canDrawerClose =
    !isUnlinkConfirmModalOpen && !isArchiveChangeConfirmModalOpen && !isExportChangeConfirmModalOpen;

  const renderContent = () => {
    switch (currentStep) {
      case 'LINK':
        return (
          <PotentialChangeSignalsLinking
            potentialChange={selectedPotentialChange}
            selectedChangeSignals={selectedChangeSignals}
            setSelectedChangeSignals={setSelectedChangeSignals}
            onLinkChangeSignals={onLinkChangeSignals}
            changeSignals={currentChangeSignals}
            issuesCount={issuesCount}
            downtimeCount={downtimeCount}
            selectedSignalType={selectedSignalType}
            setSelectedSignalType={setSelectedSignalType}
            onLoadMore={handleLoadMore}
            hasNextPage={currentQuery.hasNextPage}
            isFetchingNextPage={currentQuery.isFetchingNextPage}
          />
        );
      case 'VIEW':
        return (
          potentialChange && <PotentialChangeSignalsList
            potentialChange={potentialChange}
            onLinkChangeSignalsClick={() => setCurrentStep('LINK')}
            onUnlinkChangeSignalClick={onUnlinkChangeSignalClick}
            isUnlinkingChangeSignals={isUnlinkingChangeSignals}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Drawer.Root open={isOpen} onClose={canDrawerClose ? onClose : () => {}} maxWidth={fullsceen ? 'none' : '3xl'}>
        <Drawer.Header className="p-4 flex flex-row gap-2" onClose={onDrawerClose} rightContent={drawerRightContent}>
          <Drawer.Title className={'flex gap-3 items-center'}>
            {currentStep === 'LINK' && (
              <IconButton
                onClick={() => setCurrentStep('VIEW')}
                icon={ArrowLeftIcon}
                color={'secondary'}
                size="xs"
                variant={'text'}
              />
            )}
            {messages('potentialChangeLinkSignalsDrawer.linkSignals')}
          </Drawer.Title>
        </Drawer.Header>
        <Drawer.Content className="flex flex-col gap-4 pb-4">{renderContent()}</Drawer.Content>
      </Drawer.Root>
      <UnlinkConfirmationDialog
        isOpen={isUnlinkConfirmModalOpen}
        onClose={() => {
          closeUnlinkConfirmModal();
          setSignalToUnlink(null);
        }}
        onUnlinkConfirm={handleUnlinkConfirm}
        isUnlinking={isUnlinkingChangeSignals}
      />
      <ArchiveChangeConfirmationModal
        isOpen={isArchiveChangeConfirmModalOpen}
        closeModal={closeArchiveChangeConfirmModal}
        onArchive={onArchive}
      />
      <ExportChangeConfirmationModal
        changeId={selectedPotentialChange.id}
        open={isExportChangeConfirmModalOpen}
        onClose={closeExportChangeConfirmModal}
      />
    </>
  );
};
